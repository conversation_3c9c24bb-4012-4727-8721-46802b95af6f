/**
 * Gestion des rappels candidats - Interface Admin
 */

class ReminderCandidatesManager {
    constructor() {
        this.selectedReminders = new Set();
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateSelectedCount();
    }

    bindEvents() {
        // Gestion des checkboxes
        this.bindCheckboxEvents();
        
        // Gestion des modals
        this.bindModalEvents();
        
        // Gestion des statuts
        this.bindStatusEvents();
        
        // Gestion des filtres
        this.bindFilterEvents();
    }

    bindCheckboxEvents() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const reminderCheckboxes = document.querySelectorAll('.reminder-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                reminderCheckboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                    if (e.target.checked) {
                        this.selectedReminders.add(checkbox.value);
                    } else {
                        this.selectedReminders.delete(checkbox.value);
                    }
                });
                this.updateSelectedCount();
            });
        }

        reminderCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedReminders.add(e.target.value);
                } else {
                    this.selectedReminders.delete(e.target.value);
                }
                this.updateSelectedCount();
                this.updateSelectAllCheckbox();
            });
        });
    }

    bindModalEvents() {
        // Modal d'email individuel
        this.bindIndividualEmailModal();
        
        // Modal d'email groupé
        this.bindBulkEmailModal();
    }

    bindIndividualEmailModal() {
        const modal = document.getElementById('individual-email-modal');
        const form = document.getElementById('individual-email-form');
        const cancelBtn = document.getElementById('cancel-individual-email');

        document.querySelectorAll('.send-individual-email').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const reminderId = button.dataset.reminderId;
                const candidateName = button.dataset.candidateName;
                const candidateEmail = button.dataset.candidateEmail;

                this.openIndividualEmailModal(reminderId, candidateName, candidateEmail);
            });
        });

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeModal(modal);
            });
        }

        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendIndividualEmail(form);
            });
        }
    }

    bindBulkEmailModal() {
        const modal = document.getElementById('bulk-email-modal');
        const form = document.getElementById('bulk-email-form');
        const cancelBtn = document.getElementById('cancel-bulk-email');
        const bulkEmailBtn = document.getElementById('bulk-email-btn');

        if (bulkEmailBtn) {
            bulkEmailBtn.addEventListener('click', () => {
                if (this.selectedReminders.size === 0) return;
                this.openBulkEmailModal();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.closeModal(modal);
            });
        }

        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendBulkEmails(form);
            });
        }
    }

    bindStatusEvents() {
        document.querySelectorAll('.update-status').forEach(select => {
            select.addEventListener('change', (e) => {
                const reminderId = select.dataset.reminderId;
                const status = e.target.value;
                this.updateStatus(reminderId, status);
            });
        });
    }

    bindFilterEvents() {
        // Auto-submit du formulaire de filtre quand on change les sélections
        const filterForm = document.querySelector('form[action*="reminder-candidates"]');
        if (filterForm) {
            const selects = filterForm.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', () => {
                    // Optionnel : auto-submit du formulaire
                    // filterForm.submit();
                });
            });
        }
    }

    updateSelectedCount() {
        const selectedCountSpan = document.getElementById('selected-count');
        const bulkEmailBtn = document.getElementById('bulk-email-btn');
        
        if (selectedCountSpan) {
            selectedCountSpan.textContent = `${this.selectedReminders.size} candidat(s) sélectionné(s)`;
        }
        
        if (bulkEmailBtn) {
            bulkEmailBtn.disabled = this.selectedReminders.size === 0;
        }
    }

    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const reminderCheckboxes = document.querySelectorAll('.reminder-checkbox');
        
        if (selectAllCheckbox && reminderCheckboxes.length > 0) {
            const checkedCount = document.querySelectorAll('.reminder-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === reminderCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < reminderCheckboxes.length;
        }
    }

    openIndividualEmailModal(reminderId, candidateName, candidateEmail) {
        const modal = document.getElementById('individual-email-modal');
        
        document.getElementById('individual-reminder-id').value = reminderId;
        document.getElementById('individual-recipient').textContent = `${candidateName} (${candidateEmail})`;
        document.getElementById('individual-subject').value = 'Rappel - Recherche d\'emploi';
        document.getElementById('individual-message').value = 
            `Bonjour ${candidateName},\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d'emploi.\n\nN'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL'équipe Cyclone Placement`;

        this.showModal(modal);
    }

    openBulkEmailModal() {
        const modal = document.getElementById('bulk-email-modal');
        
        document.getElementById('bulk-recipients').textContent = `${this.selectedReminders.size} candidat(s) sélectionné(s)`;
        document.getElementById('bulk-subject').value = 'Rappel - Recherche d\'emploi';
        document.getElementById('bulk-message').value = 
            'Bonjour,\n\nNous espérons que vous allez bien. Nous souhaitions prendre de vos nouvelles concernant votre recherche d\'emploi.\n\nN\'hésitez pas à nous contacter si vous avez des questions.\n\nCordialement,\nL\'équipe Cyclone Placement';

        this.showModal(modal);
    }

    showModal(modal) {
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    }

    async sendIndividualEmail(form) {
        const formData = new FormData(form);
        const reminderId = document.getElementById('individual-reminder-id').value;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        try {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Envoi en cours...';

            const response = await fetch(`/admin/reminder-candidates/${reminderId}/send-email`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(data.message, 'success');
                this.closeModal(document.getElementById('individual-email-modal'));
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showNotification('Erreur: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('Une erreur est survenue', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Envoyer';
        }
    }

    async sendBulkEmails(form) {
        const formData = new FormData(form);
        formData.append('reminder_ids', JSON.stringify(Array.from(this.selectedReminders)));
        const submitBtn = form.querySelector('button[type="submit"]');
        
        try {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Envoi en cours...';

            const response = await fetch('/admin/reminder-candidates/send-bulk-emails', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification(data.message, 'success');
                if (data.errors && data.errors.length > 0) {
                    this.showNotification('Erreurs: ' + data.errors.join('\n'), 'warning');
                }
                this.closeModal(document.getElementById('bulk-email-modal'));
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showNotification('Erreur: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('Une erreur est survenue', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Envoyer à tous';
        }
    }

    async updateStatus(reminderId, status) {
        try {
            const response = await fetch(`/admin/reminder-candidates/${reminderId}/update-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ response_status: status })
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Statut mis à jour avec succès', 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification('Erreur: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('Une erreur est survenue', 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Créer une notification toast simple
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${this.getNotificationClasses(type)}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Supprimer la notification après 5 secondes
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    getNotificationClasses(type) {
        switch (type) {
            case 'success':
                return 'bg-green-500 text-white';
            case 'error':
                return 'bg-red-500 text-white';
            case 'warning':
                return 'bg-yellow-500 text-white';
            default:
                return 'bg-blue-500 text-white';
        }
    }
}

// Initialiser le gestionnaire quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    new ReminderCandidatesManager();
});
