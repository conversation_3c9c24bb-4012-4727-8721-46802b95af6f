<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\Laravel\Eloquent\Model;
use Carbon\Carbon;

class ReminderCandidate extends Model
{
    use HasFactory;

    protected $connection = 'mongodb';

    protected $table = 'reminder_candidates';

    protected $fillable = [
        'user_id',
        'search_work',
        'last_reminder_sent_at',
        'reminder_count',
        'response_status'
    ];

    protected $casts = [
        'search_work' => 'boolean',
        'last_reminder_sent_at' => 'datetime',
        'reminder_count' => 'integer',
    ];

    // Constantes pour les statuts de réponse
    const RESPONSE_STATUS_YES = 'yes';
    const RESPONSE_STATUS_NO = 'no';
    const RESPONSE_STATUS_NO_RESPONSE = 'no_response';

    /**
     * Relation avec le modèle User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Calculer la prochaine date de rappel
     */
    public function getNextReminderDateAttribute()
    {
        if (!$this->last_reminder_sent_at) {
            return null;
        }

        // Logique de rappel : tous les 15 jours
        return $this->last_reminder_sent_at->addDays(15);
    }

    /**
     * Vérifier si un rappel est dû
     */
    public function isReminderDue()
    {
        if (!$this->last_reminder_sent_at) {
            return false;
        }

        return now()->greaterThanOrEqualTo($this->next_reminder_date);
    }

    /**
     * Obtenir le statut de réponse formaté
     */
    public function getFormattedResponseStatusAttribute()
    {
        switch ($this->response_status) {
            case self::RESPONSE_STATUS_YES:
                return 'Oui';
            case self::RESPONSE_STATUS_NO:
                return 'Non';
            case self::RESPONSE_STATUS_NO_RESPONSE:
            default:
                return 'Pas de réponse';
        }
    }

    /**
     * Obtenir la couleur du badge selon le statut
     */
    public function getStatusBadgeColorAttribute()
    {
        switch ($this->response_status) {
            case self::RESPONSE_STATUS_YES:
                return 'bg-green-100 text-green-800';
            case self::RESPONSE_STATUS_NO:
                return 'bg-red-100 text-red-800';
            case self::RESPONSE_STATUS_NO_RESPONSE:
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Incrémenter le compteur de rappels
     */
    public function incrementReminderCount()
    {
        $this->increment('reminder_count');
        $this->update(['last_reminder_sent_at' => now()]);
    }

    /**
     * Scope pour filtrer par statut de réponse
     */
    public function scopeByResponseStatus($query, $status)
    {
        return $query->where('response_status', $status);
    }

    /**
     * Scope pour les rappels en retard
     */
    public function scopeOverdue($query)
    {
        return $query->where('last_reminder_sent_at', '<=', now()->subDays(15));
    }

    /**
     * Scope pour recherche par nom/email du candidat
     */
    public function scopeSearchByCandidate($query, $search)
    {
        return $query->whereHas('user', function ($q) use ($search) {
            $q->where('email', 'like', "%{$search}%")
              ->orWhereHas('civilityRelation', function ($subQuery) use ($search) {
                  $subQuery->where('first_name', 'like', "%{$search}%")
                           ->orWhere('last_name', 'like', "%{$search}%");
              });
        });
    }
}
