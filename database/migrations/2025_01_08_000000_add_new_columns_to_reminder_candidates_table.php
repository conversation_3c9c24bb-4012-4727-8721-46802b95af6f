<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reminder_candidates', function (Blueprint $table) {
            $table->integer('reminder_count')->default(0)->after('last_reminder_sent_at');
            $table->string('response_status')->default('no_response')->after('reminder_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reminder_candidates', function (Blueprint $table) {
            $table->dropColumn(['reminder_count', 'response_status']);
        });
    }
};
