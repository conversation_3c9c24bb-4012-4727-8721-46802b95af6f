<?php

// Script de test pour vérifier les statistiques des rappels candidats
// À exécuter depuis la racine du projet : php test_reminder_stats.php

require_once 'vendor/autoload.php';

// Charger l'application Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ReminderCandidate;

try {
    echo "=== Test des statistiques des rappels candidats ===\n\n";
    
    // Test 1: Compter le total
    $total = ReminderCandidate::count();
    echo "Total des rappels: {$total}\n";
    
    // Test 2: Compter par statut
    $yesCount = ReminderCandidate::where('response_status', 'yes')->count();
    $noCount = ReminderCandidate::where('response_status', 'no')->count();
    $noResponseCount = ReminderCandidate::where('response_status', 'no_response')->count();
    
    echo "Réponses 'Oui': {$yesCount}\n";
    echo "Réponses 'Non': {$noCount}\n";
    echo "Pas de réponse: {$noResponseCount}\n";
    
    // Test 3: Moyenne des rappels
    $averageReminders = ReminderCandidate::avg('reminder_count') ?? 0;
    echo "Moyenne des rappels: " . round($averageReminders, 1) . "\n";
    
    // Test 4: Quelques rappels avec utilisateurs
    $reminders = ReminderCandidate::with('user')->limit(3)->get();
    echo "\nPremiers rappels:\n";
    foreach ($reminders as $reminder) {
        echo "- User ID: {$reminder->user_id}, Email: " . ($reminder->user->email ?? 'N/A') . "\n";
    }
    
    echo "\n✅ Tous les tests sont passés avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
